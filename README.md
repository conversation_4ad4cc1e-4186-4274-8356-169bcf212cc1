# Annuaire Services - Thème WordPress

Un thème WordPress moderne et responsive pour un annuaire de services tunisiens, similaire à Yelp mais adapté au marché local tunisien.

## 🌟 Caractéristiques

### Design et Interface
- **Design moderne et épuré** avec une approche mobile-first
- **Interface entièrement en français** adaptée au marché tunisien
- **Palette de couleurs** : Bleu foncé (#00215E) et Jaune accent (#FFC55A)
- **Responsive design** optimisé pour smartphones, tablettes et desktop
- **Animations fluides** et interactions utilisateur intuitives

### Fonctionnalités Principales
- **Barre de recherche proéminente** avec suggestions automatiques
- **Système de catégories** avec sous-catégories organisées
- **Widget de carte interactive** (prêt pour intégration Google Maps)
- **Filtres avancés** : localisation, catégorie, note, distance
- **Cartes de services** avec informations complètes
- **Boutons de contact** : téléphone et WhatsApp
- **Système de notation** avec étoiles et avis
- **Menu mobile** avec navigation hamburger

### Catégories de Services Incluses
1. **🏠 Besoins Domestiques** : Ménage, Plomberie, Électricité, Climatisation, Jardinage, Peinture
2. **🚗 Véhicules** : Mécanique, Carrosserie, Lavage, Pneus, Dépannage, Contrôle technique
3. **💼 Services Professionnels** : Comptabilité, Juridique, Marketing, Traduction, Consulting, Formation
4. **👨‍⚕️ Santé et Bien-être** : Médecins, Dentistes, Kinésithérapie, Pharmacies, Beauté, Fitness
5. **🍽️ Restauration** : Restaurants, Fast Food, Cafés, Pâtisseries, Traiteurs, Livraison
6. **🎓 Éducation** : Cours particuliers, Langues, Informatique, Musique, Sport, Garde d'enfants

## 🚀 Installation

### Prérequis
- WordPress 5.0 ou plus récent
- PHP 7.4 ou plus récent
- MySQL 5.6 ou plus récent

### Installation du Thème

1. **Téléchargement**
   ```bash
   # Cloner ou télécharger les fichiers du thème
   git clone [URL_DU_REPO] annuaire-services
   ```

2. **Installation via WordPress Admin**
   - Aller dans `Apparence > Thèmes`
   - Cliquer sur "Ajouter un thème"
   - Télécharger le fichier ZIP du thème
   - Activer le thème

3. **Installation manuelle**
   - Copier le dossier du thème dans `/wp-content/themes/`
   - Aller dans `Apparence > Thèmes` et activer "Annuaire Services"

### Configuration Initiale

1. **Menus**
   - Aller dans `Apparence > Menus`
   - Créer un menu principal et l'assigner à "Menu Principal"

2. **Widgets**
   - Configurer les widgets dans `Apparence > Widgets`
   - Zones disponibles : Sidebar Principal, Footer Widget 1, Footer Widget 2

3. **Personnalisation**
   - Aller dans `Apparence > Personnaliser`
   - Configurer le logo, les couleurs et les options du thème

## 📁 Structure des Fichiers

```
annuaire-services/
├── style.css              # Styles CSS principaux
├── index.php              # Template de la page d'accueil
├── header.php             # En-tête du site
├── footer.php             # Pied de page
├── functions.php          # Fonctions WordPress
├── js/
│   └── main.js           # JavaScript principal
├── images/               # Images du thème (à créer)
├── inc/                  # Fichiers d'inclusion (à créer)
└── README.md            # Documentation
```

## 🎨 Personnalisation

### Couleurs
Les couleurs principales sont définies dans `style.css` via des variables CSS :
```css
:root {
    --primary-color: #00215E;    /* Bleu foncé */
    --accent-color: #FFC55A;     /* Jaune accent */
    --text-dark: #333;           /* Texte foncé */
    --text-light: #666;          /* Texte clair */
    --bg-light: #f8f9fa;         /* Arrière-plan clair */
}
```

### Ajout de Services
Pour ajouter des services, créez des articles WordPress avec :
- **Titre** : Nom du service
- **Contenu** : Description détaillée
- **Métadonnées personnalisées** :
  - `_service_phone` : Numéro de téléphone
  - `_service_whatsapp` : Numéro WhatsApp
  - `_service_location` : Localisation
  - `_service_rating` : Note (1-5)

### Intégration Google Maps
Pour activer la carte interactive :
1. Obtenir une clé API Google Maps
2. Modifier le fichier `js/main.js` dans la fonction `initializeMap()`
3. Remplacer le placeholder par l'intégration Google Maps

## 🔧 Fonctionnalités Avancées

### Recherche AJAX
Le thème inclut une base pour la recherche AJAX. Pour l'activer :
1. Implémenter la logique dans `functions.php` (fonction `annuaire_services_search_ajax`)
2. Connecter avec une base de données de services
3. Modifier `js/main.js` pour utiliser les vraies données

### Filtres Dynamiques
Les filtres sont fonctionnels côté frontend. Pour les connecter au backend :
1. Modifier la fonction `applyFilters()` dans `js/main.js`
2. Implémenter les requêtes WordPress correspondantes
3. Ajouter la pagination pour les résultats

### Géolocalisation
Pour ajouter la géolocalisation :
1. Utiliser l'API HTML5 Geolocation
2. Intégrer avec Google Maps ou OpenStreetMap
3. Calculer les distances entre utilisateur et services

## 📱 Responsive Design

Le thème est optimisé pour :
- **Mobile** : < 768px
- **Tablette** : 768px - 1024px
- **Desktop** : > 1024px

Points de rupture principaux dans `style.css` :
```css
@media (max-width: 768px) { /* Tablette et mobile */ }
@media (max-width: 480px) { /* Mobile uniquement */ }
```

## 🔍 SEO et Performance

### Optimisations SEO
- Balises meta appropriées
- Structure HTML sémantique
- URLs conviviales
- Schema.org markup (à implémenter)

### Performance
- CSS et JS minifiés en production
- Images optimisées
- Lazy loading des images
- Cache browser optimisé

## 🌐 Localisation

Le thème est entièrement en français avec :
- Interface utilisateur en français
- Villes tunisiennes prédéfinies
- Catégories adaptées au marché local
- Format de numéros de téléphone tunisiens

## 🤝 Contribution

Pour contribuer au projet :
1. Fork le repository
2. Créer une branche feature
3. Commiter les changements
4. Pousser vers la branche
5. Créer une Pull Request

## 📄 Licence

Ce thème est sous licence GPL v2 ou ultérieure.

## 🆘 Support

Pour obtenir de l'aide :
- Consulter la documentation WordPress
- Vérifier les issues GitHub
- Contacter l'équipe de développement

## 🔄 Mises à Jour

### Version 1.0.0
- Version initiale
- Design responsive
- Fonctionnalités de base
- Interface en français

### Roadmap
- [ ] Intégration Google Maps
- [ ] Système de reviews
- [ ] Dashboard pour les prestataires
- [ ] Application mobile
- [ ] Paiements en ligne
- [ ] Système de réservation

---

**Développé avec ❤️ pour connecter les Tunisiens avec les meilleurs services locaux.**
