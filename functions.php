<?php
/**
 * Annuaire Services Theme Functions
 * Fonctions et configuration du thème pour l'annuaire des services tunisiens
 */

// Empêcher l'accès direct au fichier
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Configuration du thème
 */
function annuaire_services_setup() {
    // Support pour les titres automatiques
    add_theme_support('title-tag');
    
    // Support pour les images à la une
    add_theme_support('post-thumbnails');
    
    // Support pour les menus
    add_theme_support('menus');
    
    // Support pour les formats de contenu
    add_theme_support('post-formats', array('aside', 'gallery', 'quote', 'image', 'video'));
    
    // Support pour le logo personnalisé
    add_theme_support('custom-logo', array(
        'height'      => 60,
        'width'       => 200,
        'flex-height' => true,
        'flex-width'  => true,
    ));
    
    // Support pour les couleurs personnalisées
    add_theme_support('custom-background');
    
    // Enregistrer les menus
    register_nav_menus(array(
        'primary' => __('Menu Principal', 'annuaire-services'),
        'footer'  => __('Menu Footer', 'annuaire-services'),
    ));
    
    // Définir la taille des images
    set_post_thumbnail_size(300, 200, true);
    add_image_size('service-thumbnail', 150, 150, true);
    add_image_size('category-thumbnail', 80, 80, true);
}
add_action('after_setup_theme', 'annuaire_services_setup');

/**
 * Enqueue des styles et scripts
 */
function annuaire_services_scripts() {
    // Style principal
    wp_enqueue_style('annuaire-services-style', get_stylesheet_uri(), array(), '1.0.0');
    
    // Font Awesome (déjà inclus dans header.php, mais on peut l'ajouter ici aussi)
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css', array(), '6.0.0');
    
    // Google Fonts
    wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap', array(), null);
    
    // Script principal du thème
    wp_enqueue_script('annuaire-services-script', get_template_directory_uri() . '/js/main.js', array('jquery'), '1.0.0', true);
    
    // Localisation pour AJAX
    wp_localize_script('annuaire-services-script', 'annuaire_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce'    => wp_create_nonce('annuaire_nonce'),
    ));
}
add_action('wp_enqueue_scripts', 'annuaire_services_scripts');

/**
 * Enregistrer les zones de widgets
 */
function annuaire_services_widgets_init() {
    register_sidebar(array(
        'name'          => __('Sidebar Principal', 'annuaire-services'),
        'id'            => 'sidebar-1',
        'description'   => __('Zone de widgets pour la sidebar principale', 'annuaire-services'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer Widget 1', 'annuaire-services'),
        'id'            => 'footer-1',
        'description'   => __('Zone de widgets pour le footer - colonne 1', 'annuaire-services'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer Widget 2', 'annuaire-services'),
        'id'            => 'footer-2',
        'description'   => __('Zone de widgets pour le footer - colonne 2', 'annuaire-services'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'annuaire_services_widgets_init');

/**
 * Personnaliser l'extrait
 */
function annuaire_services_excerpt_length($length) {
    return 20;
}
add_filter('excerpt_length', 'annuaire_services_excerpt_length');

function annuaire_services_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'annuaire_services_excerpt_more');

/**
 * Ajouter des classes CSS personnalisées au body
 */
function annuaire_services_body_classes($classes) {
    if (is_front_page()) {
        $classes[] = 'homepage';
    }
    
    if (is_mobile()) {
        $classes[] = 'mobile-device';
    }
    
    return $classes;
}
add_filter('body_class', 'annuaire_services_body_classes');

/**
 * Fonction pour obtenir les catégories de services
 */
function get_service_categories() {
    return array(
        'domestique' => array(
            'name' => 'Besoins Domestiques',
            'icon' => '🏠',
            'subcategories' => array(
                'menage' => 'Ménage et Nettoyage',
                'plomberie' => 'Plomberie',
                'electricite' => 'Électricité',
                'climatisation' => 'Climatisation',
                'jardinage' => 'Jardinage',
                'peinture' => 'Peinture et Décoration'
            )
        ),
        'vehicules' => array(
            'name' => 'Véhicules',
            'icon' => '🚗',
            'subcategories' => array(
                'mecanique' => 'Mécanique Auto',
                'carrosserie' => 'Carrosserie',
                'lavage' => 'Lavage Auto',
                'pneus' => 'Pneus et Jantes',
                'depannage' => 'Dépannage',
                'controle' => 'Contrôle Technique'
            )
        ),
        'professionnel' => array(
            'name' => 'Services Professionnels',
            'icon' => '💼',
            'subcategories' => array(
                'comptabilite' => 'Comptabilité',
                'juridique' => 'Juridique',
                'marketing' => 'Marketing Digital',
                'traduction' => 'Traduction',
                'consulting' => 'Consulting',
                'formation' => 'Formation'
            )
        ),
        'sante' => array(
            'name' => 'Santé et Bien-être',
            'icon' => '👨‍⚕️',
            'subcategories' => array(
                'medecins' => 'Médecins',
                'dentistes' => 'Dentistes',
                'kinesitherapie' => 'Kinésithérapie',
                'pharmacies' => 'Pharmacies',
                'beaute' => 'Salons de Beauté',
                'fitness' => 'Fitness'
            )
        ),
        'restauration' => array(
            'name' => 'Restauration',
            'icon' => '🍽️',
            'subcategories' => array(
                'restaurants' => 'Restaurants',
                'fastfood' => 'Fast Food',
                'cafes' => 'Cafés',
                'patisseries' => 'Pâtisseries',
                'traiteurs' => 'Traiteurs',
                'livraison' => 'Livraison'
            )
        ),
        'education' => array(
            'name' => 'Éducation',
            'icon' => '🎓',
            'subcategories' => array(
                'cours' => 'Cours Particuliers',
                'langues' => 'Langues',
                'informatique' => 'Informatique',
                'musique' => 'Musique',
                'sport' => 'Sport',
                'garde' => 'Garde d\'Enfants'
            )
        )
    );
}

/**
 * Fonction pour obtenir les villes tunisiennes
 */
function get_tunisian_cities() {
    return array(
        'tunis' => 'Tunis',
        'sfax' => 'Sfax',
        'sousse' => 'Sousse',
        'kairouan' => 'Kairouan',
        'bizerte' => 'Bizerte',
        'gabes' => 'Gabès',
        'ariana' => 'Ariana',
        'gafsa' => 'Gafsa',
        'monastir' => 'Monastir',
        'ben-arous' => 'Ben Arous',
        'medenine' => 'Médenine',
        'nabeul' => 'Nabeul',
        'kasserine' => 'Kasserine',
        'mahdia' => 'Mahdia',
        'sidi-bouzid' => 'Sidi Bouzid',
        'jendouba' => 'Jendouba',
        'tataouine' => 'Tataouine',
        'le-kef' => 'Le Kef',
        'tozeur' => 'Tozeur',
        'kebili' => 'Kébili',
        'siliana' => 'Siliana',
        'beja' => 'Béja',
        'zaghouan' => 'Zaghouan',
        'manouba' => 'Manouba'
    );
}

/**
 * AJAX pour la recherche de services
 */
function annuaire_services_search_ajax() {
    check_ajax_referer('annuaire_nonce', 'nonce');
    
    $search_term = sanitize_text_field($_POST['search_term']);
    $category = sanitize_text_field($_POST['category']);
    $location = sanitize_text_field($_POST['location']);
    
    // Ici vous pouvez implémenter la logique de recherche
    // Pour l'instant, on retourne un exemple de réponse
    
    $response = array(
        'success' => true,
        'data' => array(
            'results' => array(),
            'total' => 0
        )
    );
    
    wp_send_json($response);
}
add_action('wp_ajax_search_services', 'annuaire_services_search_ajax');
add_action('wp_ajax_nopriv_search_services', 'annuaire_services_search_ajax');

/**
 * Ajouter des métaboxes personnalisées pour les services
 */
function annuaire_services_add_meta_boxes() {
    add_meta_box(
        'service_details',
        'Détails du Service',
        'annuaire_services_meta_box_callback',
        'post',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'annuaire_services_add_meta_boxes');

function annuaire_services_meta_box_callback($post) {
    wp_nonce_field('annuaire_services_meta_box', 'annuaire_services_meta_box_nonce');
    
    $phone = get_post_meta($post->ID, '_service_phone', true);
    $whatsapp = get_post_meta($post->ID, '_service_whatsapp', true);
    $location = get_post_meta($post->ID, '_service_location', true);
    $rating = get_post_meta($post->ID, '_service_rating', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="service_phone">Téléphone:</label></th>';
    echo '<td><input type="text" id="service_phone" name="service_phone" value="' . esc_attr($phone) . '" /></td></tr>';
    echo '<tr><th><label for="service_whatsapp">WhatsApp:</label></th>';
    echo '<td><input type="text" id="service_whatsapp" name="service_whatsapp" value="' . esc_attr($whatsapp) . '" /></td></tr>';
    echo '<tr><th><label for="service_location">Localisation:</label></th>';
    echo '<td><input type="text" id="service_location" name="service_location" value="' . esc_attr($location) . '" /></td></tr>';
    echo '<tr><th><label for="service_rating">Note (1-5):</label></th>';
    echo '<td><input type="number" id="service_rating" name="service_rating" min="1" max="5" step="0.1" value="' . esc_attr($rating) . '" /></td></tr>';
    echo '</table>';
}

/**
 * Sauvegarder les métadonnées personnalisées
 */
function annuaire_services_save_meta_box($post_id) {
    if (!isset($_POST['annuaire_services_meta_box_nonce'])) {
        return;
    }
    
    if (!wp_verify_nonce($_POST['annuaire_services_meta_box_nonce'], 'annuaire_services_meta_box')) {
        return;
    }
    
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    $fields = array('service_phone', 'service_whatsapp', 'service_location', 'service_rating');
    
    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
        }
    }
}
add_action('save_post', 'annuaire_services_save_meta_box');

/**
 * Personnaliser le titre de la page d'accueil
 */
function annuaire_services_wp_title($title, $sep) {
    if (is_front_page()) {
        return 'Annuaire Services Tunisie - Trouvez les Meilleurs Services Locaux';
    }
    return $title;
}
add_filter('wp_title', 'annuaire_services_wp_title', 10, 2);

/**
 * Ajouter du CSS personnalisé dans l'admin
 */
function annuaire_services_admin_styles() {
    echo '<style>
        .form-table th { width: 150px; }
        .form-table input[type="text"], .form-table input[type="number"] { width: 100%; }
    </style>';
}
add_action('admin_head', 'annuaire_services_admin_styles');
?>
