<footer class="site-footer">
    <div class="container">
        <div class="footer-content">
            <!-- Section À Propos -->
            <div class="footer-section">
                <h3>À Propos d'Annuaire Services</h3>
                <p style="margin-bottom: 1rem; opacity: 0.8; line-height: 1.6;">
                    Votre plateforme de référence pour trouver les meilleurs services en Tunisie. 
                    Nous connectons les clients avec des professionnels qualifiés dans tous les domaines.
                </p>
                <div style="display: flex; gap: 1rem; margin-top: 1rem;">
                    <a href="#" style="color: var(--accent-color); font-size: 1.5rem; transition: transform 0.3s;">
                        <i class="fab fa-facebook"></i>
                    </a>
                    <a href="#" style="color: var(--accent-color); font-size: 1.5rem; transition: transform 0.3s;">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" style="color: var(--accent-color); font-size: 1.5rem; transition: transform 0.3s;">
                        <i class="fab fa-linkedin"></i>
                    </a>
                    <a href="#" style="color: var(--accent-color); font-size: 1.5rem; transition: transform 0.3s;">
                        <i class="fab fa-twitter"></i>
                    </a>
                </div>
            </div>
            
            <!-- Section Catégories Populaires -->
            <div class="footer-section">
                <h3>Catégories Populaires</h3>
                <ul style="line-height: 2;">
                    <li><a href="#">Plomberie</a></li>
                    <li><a href="#">Électricité</a></li>
                    <li><a href="#">Mécanique Auto</a></li>
                    <li><a href="#">Ménage</a></li>
                    <li><a href="#">Jardinage</a></li>
                    <li><a href="#">Peinture</a></li>
                    <li><a href="#">Climatisation</a></li>
                </ul>
            </div>
            
            <!-- Section Villes -->
            <div class="footer-section">
                <h3>Villes Principales</h3>
                <ul style="line-height: 2;">
                    <li><a href="#">Services à Tunis</a></li>
                    <li><a href="#">Services à Sfax</a></li>
                    <li><a href="#">Services à Sousse</a></li>
                    <li><a href="#">Services à Kairouan</a></li>
                    <li><a href="#">Services à Bizerte</a></li>
                    <li><a href="#">Services à Gabès</a></li>
                    <li><a href="#">Services à Ariana</a></li>
                </ul>
            </div>
            
            <!-- Section Contact -->
            <div class="footer-section">
                <h3>Contactez-Nous</h3>
                <div style="line-height: 2;">
                    <p style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                        <i class="fas fa-envelope"></i>
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </p>
                    <p style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                        <i class="fas fa-phone"></i>
                        <a href="tel:+21670123456">+216 70 123 456</a>
                    </p>
                    <p style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                        <i class="fab fa-whatsapp"></i>
                        <a href="https://wa.me/21670123456">WhatsApp Support</a>
                    </p>
                    <p style="display: flex; align-items: flex-start; gap: 0.5rem; margin-bottom: 1rem;">
                        <i class="fas fa-map-marker-alt" style="margin-top: 0.2rem;"></i>
                        <span>Avenue Habib Bourguiba<br>Tunis, Tunisie</span>
                    </p>
                </div>
                
                <!-- Bouton d'ajout de service -->
                <a href="#" style="background: var(--accent-color); color: var(--primary-color); padding: 0.8rem 1.5rem; border-radius: 5px; font-weight: 600; text-decoration: none; display: inline-block; margin-top: 1rem; transition: all 0.3s;">
                    <i class="fas fa-plus"></i> Ajouter Votre Service
                </a>
            </div>
        </div>
        
        <!-- Section liens légaux -->
        <div style="border-top: 1px solid rgba(255, 255, 255, 0.2); padding-top: 2rem; margin-top: 2rem;">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 1rem;">
                <div style="display: flex; gap: 2rem; flex-wrap: wrap;">
                    <a href="#" style="color: var(--white); text-decoration: none; opacity: 0.8; transition: opacity 0.3s;">
                        Conditions d'Utilisation
                    </a>
                    <a href="#" style="color: var(--white); text-decoration: none; opacity: 0.8; transition: opacity 0.3s;">
                        Politique de Confidentialité
                    </a>
                    <a href="#" style="color: var(--white); text-decoration: none; opacity: 0.8; transition: opacity 0.3s;">
                        Mentions Légales
                    </a>
                    <a href="#" style="color: var(--white); text-decoration: none; opacity: 0.8; transition: opacity 0.3s;">
                        FAQ
                    </a>
                </div>
                
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <span style="opacity: 0.8;">Suivez-nous:</span>
                    <div style="display: flex; gap: 0.5rem;">
                        <a href="#" style="color: var(--accent-color); font-size: 1.2rem;">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <a href="#" style="color: var(--accent-color); font-size: 1.2rem;">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" style="color: var(--accent-color); font-size: 1.2rem;">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Copyright -->
        <div class="footer-bottom">
            <p>&copy; <?php echo date('Y'); ?> Annuaire Services Tunisie. Tous droits réservés.</p>
            <p style="font-size: 0.9rem; margin-top: 0.5rem;">
                Développé avec ❤️ pour connecter les Tunisiens avec les meilleurs services locaux.
            </p>
        </div>
    </div>
</footer>

<!-- Bouton de retour en haut -->
<button id="back-to-top" style="position: fixed; bottom: 2rem; right: 2rem; background: var(--primary-color); color: var(--white); border: none; border-radius: 50%; width: 50px; height: 50px; font-size: 1.2rem; cursor: pointer; box-shadow: var(--shadow); opacity: 0; visibility: hidden; transition: all 0.3s; z-index: 1000;">
    <i class="fas fa-arrow-up"></i>
</button>

<style>
/* Styles pour les liens sociaux avec effet hover */
.footer-section a[href*="facebook"]:hover,
.footer-section a[href*="instagram"]:hover,
.footer-section a[href*="linkedin"]:hover,
.footer-section a[href*="twitter"]:hover {
    transform: translateY(-3px);
}

/* Effet hover pour le bouton d'ajout de service */
.footer-section a[href="#"]:hover {
    background: #e6b350 !important;
    transform: translateY(-2px);
}

/* Responsive pour le footer */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 3rem;
    }
    
    .footer-section ul {
        columns: 2;
        column-gap: 2rem;
    }
    
    .footer-bottom > div:first-child {
        flex-direction: column;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .footer-section ul {
        columns: 1;
    }
    
    #back-to-top {
        bottom: 1rem;
        right: 1rem;
        width: 45px;
        height: 45px;
    }
}

/* Animation pour le bouton de retour en haut */
#back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

#back-to-top:hover {
    background: var(--accent-color);
    color: var(--primary-color);
    transform: translateY(-3px);
}
</style>

<script>
// JavaScript pour le bouton de retour en haut
document.addEventListener('DOMContentLoaded', function() {
    const backToTopBtn = document.getElementById('back-to-top');
    
    // Afficher/masquer le bouton selon le scroll
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopBtn.classList.add('visible');
        } else {
            backToTopBtn.classList.remove('visible');
        }
    });
    
    // Fonction de retour en haut
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    // JavaScript pour les filtres (simulation)
    const distanceFilter = document.getElementById('distance-filter');
    const distanceValue = document.getElementById('distance-value');
    
    if (distanceFilter && distanceValue) {
        distanceFilter.addEventListener('input', function() {
            distanceValue.textContent = this.value + ' km';
        });
    }
    
    // Animation au scroll pour les éléments
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observer les cartes de service
    document.querySelectorAll('.service-card, .category-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});
</script>

<?php wp_footer(); ?>
</body>
</html>
