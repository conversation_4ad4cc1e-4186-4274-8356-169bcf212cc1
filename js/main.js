/**
 * JavaScript principal pour Annuaire Services
 * Gestion des interactions et fonctionnalités dynamiques
 */

(function($) {
    'use strict';

    // Variables globales
    let currentFilters = {
        location: '',
        category: '',
        rating: '',
        distance: 10
    };

    /**
     * Initialisation au chargement de la page
     */
    $(document).ready(function() {
        initializeComponents();
        bindEvents();
        initializeAnimations();
    });

    /**
     * Initialiser les composants
     */
    function initializeComponents() {
        // Initialiser la carte (placeholder)
        initializeMap();
        
        // Initialiser les filtres
        initializeFilters();
        
        // Initialiser la recherche
        initializeSearch();
        
        // Initialiser les animations au scroll
        initializeScrollAnimations();
    }

    /**
     * Lier les événements
     */
    function bindEvents() {
        // Recherche en temps réel
        $('.search-input').on('input', debounce(handleSearch, 300));
        
        // Soumission du formulaire de recherche
        $('.search-form').on('submit', handleSearchSubmit);
        
        // Filtres
        $('.filter-select, .filter-input').on('change', handleFilterChange);
        $('#distance-filter').on('input', handleDistanceChange);
        
        // Cartes de catégories
        $('.category-card').on('click', handleCategoryClick);
        
        // Cartes de services
        $('.service-card').on('click', handleServiceClick);
        
        // Boutons de contact
        $('.contact-btn').on('click', handleContactClick);
        
        // Menu mobile
        $('.mobile-menu-toggle').on('click', toggleMobileMenu);
        $('.mobile-menu-close, .mobile-menu-overlay').on('click', closeMobileMenu);
        
        // Bouton retour en haut
        $('#back-to-top').on('click', scrollToTop);
        $(window).on('scroll', handleScroll);
        
        // Navigation fluide
        $('a[href^="#"]').on('click', handleSmoothScroll);
    }

    /**
     * Initialiser les animations
     */
    function initializeAnimations() {
        // Animation des cartes au survol
        $('.category-card, .service-card').hover(
            function() {
                $(this).addClass('hovered');
            },
            function() {
                $(this).removeClass('hovered');
            }
        );
    }

    /**
     * Initialiser la carte (placeholder pour l'instant)
     */
    function initializeMap() {
        const mapContainer = $('#map-placeholder');
        if (mapContainer.length) {
            // Ici vous pouvez intégrer Google Maps ou Leaflet
            mapContainer.html(`
                <div style="text-align: center; padding: 2rem;">
                    <i class="fas fa-map-marker-alt" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem; display: block;"></i>
                    <p style="margin-bottom: 0.5rem;">Carte Interactive des Services</p>
                    <p style="font-size: 0.9rem; color: #666;">Intégration Google Maps à venir</p>
                    <button class="search-btn" style="margin-top: 1rem;" onclick="alert('Fonctionnalité à venir')">
                        Activer la Géolocalisation
                    </button>
                </div>
            `);
        }
    }

    /**
     * Initialiser les filtres
     */
    function initializeFilters() {
        // Charger les filtres sauvegardés
        loadSavedFilters();
        
        // Appliquer les filtres par défaut
        applyFilters();
    }

    /**
     * Initialiser la recherche
     */
    function initializeSearch() {
        // Ajouter des suggestions de recherche
        const searchInput = $('.search-input');
        if (searchInput.length) {
            searchInput.attr('autocomplete', 'off');
            
            // Créer une liste de suggestions
            const suggestions = [
                'Plombier Tunis',
                'Électricien Sfax',
                'Mécanicien Sousse',
                'Ménage Ariana',
                'Jardinage Bizerte',
                'Dentiste Monastir',
                'Restaurant Nabeul',
                'Coiffeur Mahdia'
            ];
            
            // Ajouter l'autocomplétion (simulation)
            searchInput.on('focus', function() {
                showSearchSuggestions(suggestions);
            });
            
            searchInput.on('blur', function() {
                setTimeout(hideSearchSuggestions, 200);
            });
        }
    }

    /**
     * Initialiser les animations au scroll
     */
    function initializeScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observer les éléments à animer
        $('.category-card, .service-card, .section-title').each(function() {
            observer.observe(this);
        });
    }

    /**
     * Gérer la recherche
     */
    function handleSearch() {
        const searchTerm = $('.search-input').val();
        if (searchTerm.length >= 2) {
            performSearch(searchTerm);
        }
    }

    /**
     * Gérer la soumission du formulaire de recherche
     */
    function handleSearchSubmit(e) {
        e.preventDefault();
        const searchTerm = $('.search-input').val();
        if (searchTerm.trim()) {
            performSearch(searchTerm);
            // Scroll vers les résultats
            $('html, body').animate({
                scrollTop: $('.services-section').offset().top - 100
            }, 800);
        }
    }

    /**
     * Effectuer la recherche
     */
    function performSearch(searchTerm) {
        // Afficher un indicateur de chargement
        showLoadingIndicator();
        
        // Simulation d'une recherche AJAX
        setTimeout(function() {
            // Ici vous pouvez implémenter la vraie recherche AJAX
            console.log('Recherche pour:', searchTerm);
            hideLoadingIndicator();
            
            // Filtrer les services visibles (simulation)
            filterVisibleServices(searchTerm);
        }, 500);
    }

    /**
     * Filtrer les services visibles
     */
    function filterVisibleServices(searchTerm) {
        $('.service-card').each(function() {
            const serviceName = $(this).find('.service-name').text().toLowerCase();
            const serviceCategory = $(this).find('.service-category').text().toLowerCase();
            const serviceLocation = $(this).find('.service-location').text().toLowerCase();
            
            const searchLower = searchTerm.toLowerCase();
            
            if (serviceName.includes(searchLower) || 
                serviceCategory.includes(searchLower) || 
                serviceLocation.includes(searchLower)) {
                $(this).show().addClass('search-match');
            } else {
                $(this).hide().removeClass('search-match');
            }
        });
        
        // Afficher le nombre de résultats
        const visibleServices = $('.service-card:visible').length;
        showSearchResults(visibleServices, searchTerm);
    }

    /**
     * Gérer les changements de filtres
     */
    function handleFilterChange() {
        currentFilters.location = $('#location-filter').val();
        currentFilters.category = $('#category-filter').val();
        currentFilters.rating = $('#rating-filter').val();
        
        applyFilters();
        saveFilters();
    }

    /**
     * Gérer le changement de distance
     */
    function handleDistanceChange() {
        const distance = $('#distance-filter').val();
        $('#distance-value').text(distance + ' km');
        currentFilters.distance = distance;
        
        applyFilters();
        saveFilters();
    }

    /**
     * Appliquer les filtres
     */
    function applyFilters() {
        $('.service-card').each(function() {
            let show = true;
            
            // Filtre par localisation
            if (currentFilters.location) {
                const serviceLocation = $(this).find('.service-location').text().toLowerCase();
                if (!serviceLocation.includes(currentFilters.location.toLowerCase())) {
                    show = false;
                }
            }
            
            // Filtre par catégorie
            if (currentFilters.category) {
                const serviceCategory = $(this).find('.service-category').text().toLowerCase();
                if (!serviceCategory.includes(currentFilters.category.toLowerCase())) {
                    show = false;
                }
            }
            
            // Filtre par note
            if (currentFilters.rating) {
                const ratingText = $(this).find('.service-rating span:last-child').text();
                const rating = parseFloat(ratingText.replace(/[()]/g, ''));
                if (rating < parseFloat(currentFilters.rating)) {
                    show = false;
                }
            }
            
            if (show) {
                $(this).show().addClass('filter-match');
            } else {
                $(this).hide().removeClass('filter-match');
            }
        });
        
        // Mettre à jour le compteur
        updateResultsCounter();
    }

    /**
     * Gérer le clic sur une catégorie
     */
    function handleCategoryClick() {
        const categoryTitle = $(this).find('.category-title').text();
        
        // Animer vers la section des services
        $('html, body').animate({
            scrollTop: $('.services-section').offset().top - 100
        }, 800);
        
        // Filtrer par catégorie
        setTimeout(function() {
            $('#category-filter').val(categoryTitle.toLowerCase()).trigger('change');
        }, 400);
    }

    /**
     * Gérer le clic sur un service
     */
    function handleServiceClick(e) {
        // Ne pas déclencher si on clique sur un bouton
        if ($(e.target).hasClass('contact-btn') || $(e.target).closest('.contact-btn').length) {
            return;
        }
        
        // Ici vous pouvez ouvrir une modal ou rediriger vers une page détaillée
        const serviceName = $(this).find('.service-name').text();
        console.log('Service sélectionné:', serviceName);
        
        // Animation de sélection
        $(this).addClass('selected');
        setTimeout(() => {
            $(this).removeClass('selected');
        }, 300);
    }

    /**
     * Gérer les clics sur les boutons de contact
     */
    function handleContactClick(e) {
        e.stopPropagation();
        
        const button = $(this);
        const isPhone = button.hasClass('phone-btn');
        const isWhatsApp = button.hasClass('whatsapp-btn');
        
        // Animation du bouton
        button.addClass('clicked');
        setTimeout(() => {
            button.removeClass('clicked');
        }, 200);
        
        // Tracking (optionnel)
        if (isPhone) {
            console.log('Appel téléphonique initié');
        } else if (isWhatsApp) {
            console.log('Message WhatsApp initié');
        }
    }

    /**
     * Fonctions utilitaires
     */
    
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    function showLoadingIndicator() {
        if (!$('.loading-indicator').length) {
            $('body').append('<div class="loading-indicator">Recherche en cours...</div>');
        }
    }

    function hideLoadingIndicator() {
        $('.loading-indicator').remove();
    }

    function showSearchResults(count, term) {
        const resultsMessage = `${count} service(s) trouvé(s) pour "${term}"`;
        
        // Supprimer le message précédent
        $('.search-results-message').remove();
        
        // Ajouter le nouveau message
        $('.services-section .section-title').after(
            `<p class="search-results-message" style="text-align: center; color: var(--text-light); margin-bottom: 2rem;">${resultsMessage}</p>`
        );
    }

    function updateResultsCounter() {
        const visibleCount = $('.service-card:visible').length;
        const totalCount = $('.service-card').length;
        
        $('.results-counter').remove();
        $('.services-section .section-title').after(
            `<p class="results-counter" style="text-align: center; color: var(--text-light); margin-bottom: 2rem;">
                Affichage de ${visibleCount} sur ${totalCount} services
            </p>`
        );
    }

    function saveFilters() {
        localStorage.setItem('annuaire_filters', JSON.stringify(currentFilters));
    }

    function loadSavedFilters() {
        const saved = localStorage.getItem('annuaire_filters');
        if (saved) {
            currentFilters = JSON.parse(saved);
            
            // Appliquer les filtres sauvegardés aux éléments
            $('#location-filter').val(currentFilters.location);
            $('#category-filter').val(currentFilters.category);
            $('#rating-filter').val(currentFilters.rating);
            $('#distance-filter').val(currentFilters.distance);
            $('#distance-value').text(currentFilters.distance + ' km');
        }
    }

    function showSearchSuggestions(suggestions) {
        // Implémentation des suggestions de recherche
        console.log('Suggestions:', suggestions);
    }

    function hideSearchSuggestions() {
        $('.search-suggestions').remove();
    }

    function toggleMobileMenu() {
        $('.mobile-menu-overlay').addClass('active');
        $('body').css('overflow', 'hidden');
    }

    function closeMobileMenu() {
        $('.mobile-menu-overlay').removeClass('active');
        $('body').css('overflow', '');
    }

    function scrollToTop() {
        $('html, body').animate({ scrollTop: 0 }, 600);
    }

    function handleScroll() {
        const scrollTop = $(window).scrollTop();
        
        // Bouton retour en haut
        if (scrollTop > 300) {
            $('#back-to-top').addClass('visible');
        } else {
            $('#back-to-top').removeClass('visible');
        }
        
        // Header sticky effect
        if (scrollTop > 100) {
            $('.site-header').addClass('scrolled');
        } else {
            $('.site-header').removeClass('scrolled');
        }
    }

    function handleSmoothScroll(e) {
        const target = $(this.getAttribute('href'));
        if (target.length) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: target.offset().top - 80
            }, 800);
        }
    }

})(jQuery);
