<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Annuaire Services - Trouvez les meilleurs services en Tunisie. Plombiers, électriciens, mécaniciens et plus encore.">
    <meta name="keywords" content="services tunisie, plombier, électricien, mécanicien, annuaire, tunisien">
    
    <title><?php wp_title('|', true, 'right'); ?><?php bloginfo('name'); ?></title>
    
    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>

<header class="site-header">
    <div class="container">
        <div class="header-content">
            <!-- Logo -->
            <a href="<?php echo home_url(); ?>" class="logo">
                <i class="fas fa-map-marked-alt"></i>
                Annuaire Services
            </a>
            
            <!-- Navigation principale -->
            <nav class="main-nav">
                <ul>
                    <li><a href="<?php echo home_url(); ?>">Accueil</a></li>
                    <li><a href="#categories">Catégories</a></li>
                    <li><a href="#services">Services</a></li>
                    <li><a href="#contact">Contact</a></li>
                    <li><a href="#" class="add-service-btn" style="background: var(--accent-color); color: var(--primary-color); padding: 0.5rem 1rem; border-radius: 5px; font-weight: 600; text-decoration: none;">
                        <i class="fas fa-plus"></i> Ajouter Service
                    </a></li>
                </ul>
            </nav>
            
            <!-- Menu mobile (hamburger) -->
            <button class="mobile-menu-toggle" style="display: none; background: none; border: none; font-size: 1.5rem; color: var(--primary-color);">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </div>
</header>

<!-- Menu mobile overlay -->
<div class="mobile-menu-overlay" style="display: none;">
    <div class="mobile-menu">
        <button class="mobile-menu-close" style="background: none; border: none; font-size: 1.5rem; color: var(--white); float: right; margin-bottom: 2rem;">
            <i class="fas fa-times"></i>
        </button>
        <nav class="mobile-nav">
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 1rem;"><a href="<?php echo home_url(); ?>" style="color: var(--white); text-decoration: none; font-size: 1.2rem;">Accueil</a></li>
                <li style="margin-bottom: 1rem;"><a href="#categories" style="color: var(--white); text-decoration: none; font-size: 1.2rem;">Catégories</a></li>
                <li style="margin-bottom: 1rem;"><a href="#services" style="color: var(--white); text-decoration: none; font-size: 1.2rem;">Services</a></li>
                <li style="margin-bottom: 1rem;"><a href="#contact" style="color: var(--white); text-decoration: none; font-size: 1.2rem;">Contact</a></li>
                <li style="margin-bottom: 1rem;"><a href="#" style="background: var(--accent-color); color: var(--primary-color); padding: 0.8rem 1.5rem; border-radius: 5px; font-weight: 600; text-decoration: none; display: inline-block;">
                    <i class="fas fa-plus"></i> Ajouter Service
                </a></li>
            </ul>
        </nav>
    </div>
</div>

<style>
/* Styles pour le menu mobile */
.mobile-menu-toggle {
    display: none !important;
}

.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 33, 94, 0.95);
    z-index: 9999;
    padding: 2rem;
}

.mobile-menu {
    max-width: 400px;
    margin: 0 auto;
    padding-top: 2rem;
}

@media (max-width: 768px) {
    .main-nav {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: block !important;
    }
    
    .header-content {
        justify-content: space-between;
    }
}

/* Animation pour le menu mobile */
.mobile-menu-overlay {
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-menu {
    transform: translateY(-50px);
    transition: transform 0.3s ease;
}

.mobile-menu-overlay.active .mobile-menu {
    transform: translateY(0);
}

/* Amélioration du logo */
.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-family: 'Inter', sans-serif;
}

.logo i {
    font-size: 1.5rem;
    color: var(--accent-color);
}

/* Amélioration de la navigation */
.main-nav ul {
    align-items: center;
}

.add-service-btn {
    transition: all 0.3s ease;
}

.add-service-btn:hover {
    background: #e6b350 !important;
    transform: translateY(-2px);
}

/* Responsive amélioré */
@media (max-width: 480px) {
    .logo {
        font-size: 1.4rem;
    }
    
    .container {
        padding: 0 15px;
    }
}
</style>

<script>
// JavaScript pour le menu mobile
document.addEventListener('DOMContentLoaded', function() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const mobileOverlay = document.querySelector('.mobile-menu-overlay');
    const mobileClose = document.querySelector('.mobile-menu-close');
    
    if (mobileToggle && mobileOverlay) {
        mobileToggle.addEventListener('click', function() {
            mobileOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        });
        
        mobileClose.addEventListener('click', function() {
            mobileOverlay.classList.remove('active');
            document.body.style.overflow = '';
        });
        
        // Fermer le menu en cliquant sur l'overlay
        mobileOverlay.addEventListener('click', function(e) {
            if (e.target === mobileOverlay) {
                mobileOverlay.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
        
        // Fermer le menu avec la touche Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && mobileOverlay.classList.contains('active')) {
                mobileOverlay.classList.remove('active');
                document.body.style.overflow = '';
            }
        });
    }
});
</script>
