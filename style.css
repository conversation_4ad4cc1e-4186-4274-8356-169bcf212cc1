/*
Theme Name: Annuaire Services
Description: Thème WordPress pour l'annuaire des services tunisiens
Version: 1.0
*/

/* Variables CSS */
:root {
    --primary-color: #00215E;
    --accent-color: #FFC55A;
    --text-dark: #333;
    --text-light: #666;
    --bg-light: #f8f9fa;
    --white: #ffffff;
    --border-color: #e0e0e0;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
}

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.site-header {
    background: var(--white);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--primary-color);
    text-decoration: none;
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.main-nav a {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: color 0.3s;
}

.main-nav a:hover {
    color: var(--primary-color);
}

/* Section héro avec recherche */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #003875 100%);
    color: var(--white);
    padding: 4rem 0;
    text-align: center;
}

.hero-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Barre de recherche */
.search-container {
    max-width: 600px;
    margin: 0 auto;
    position: relative;
}

.search-form {
    display: flex;
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.search-input {
    flex: 1;
    padding: 1rem;
    border: none;
    font-size: 1rem;
    outline: none;
}

.search-btn {
    background: var(--accent-color);
    color: var(--primary-color);
    border: none;
    padding: 1rem 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s;
}

.search-btn:hover {
    background: #e6b350;
}

/* Section catégories */
.categories-section {
    padding: 4rem 0;
    background: var(--bg-light);
}

.section-title {
    text-align: center;
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 3rem;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.category-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow);
    transition: transform 0.3s, box-shadow 0.3s;
    cursor: pointer;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.category-title {
    font-size: 1.3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.subcategories {
    list-style: none;
}

.subcategories li {
    padding: 0.3rem 0;
    color: var(--text-light);
    border-bottom: 1px solid var(--border-color);
}

.subcategories li:last-child {
    border-bottom: none;
}

.subcategories a {
    text-decoration: none;
    color: inherit;
    transition: color 0.3s;
}

.subcategories a:hover {
    color: var(--primary-color);
}

/* Section carte et filtres */
.map-filters-section {
    padding: 4rem 0;
}

.map-filters-container {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
}

.map-widget {
    background: var(--bg-light);
    border-radius: var(--border-radius);
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    font-size: 1.1rem;
}

.filters-panel {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow);
    height: fit-content;
}

.filter-group {
    margin-bottom: 2rem;
}

.filter-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.filter-select,
.filter-input {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
}

/* Section services */
.services-section {
    padding: 4rem 0;
    background: var(--bg-light);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.service-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    transition: transform 0.3s;
}

.service-card:hover {
    transform: translateY(-3px);
}

.service-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.service-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.3rem;
}

.service-category {
    color: var(--text-light);
    font-size: 0.9rem;
}

.service-rating {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.stars {
    color: var(--accent-color);
}

.service-location {
    color: var(--text-light);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.service-contact {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.contact-btn {
    flex: 1;
    padding: 0.8rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s;
    text-decoration: none;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.phone-btn {
    background: var(--primary-color);
    color: var(--white);
}

.whatsapp-btn {
    background: #25D366;
    color: var(--white);
}

.contact-btn:hover {
    opacity: 0.9;
}

.service-review {
    font-style: italic;
    color: var(--text-light);
    font-size: 0.9rem;
    border-left: 3px solid var(--accent-color);
    padding-left: 1rem;
}

/* Footer */
.site-footer {
    background: var(--primary-color);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: var(--accent-color);
}

.footer-section ul {
    list-style: none;
}

.footer-section a {
    color: var(--white);
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s;
}

.footer-section a:hover {
    opacity: 1;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .main-nav ul {
        gap: 1rem;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .search-form {
        flex-direction: column;
    }
    
    .search-btn {
        padding: 1rem;
    }
    
    .map-filters-container {
        grid-template-columns: 1fr;
    }
    
    .filters-panel {
        order: -1;
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .service-contact {
        flex-direction: column;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .hero-section {
        padding: 2rem 0;
    }
    
    .categories-section,
    .map-filters-section,
    .services-section {
        padding: 2rem 0;
    }
}
